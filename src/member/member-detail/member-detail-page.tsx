import { View, Text, Image } from '@tarojs/components';
import { useState, useCallback } from 'react';
import { AtIcon } from 'taro-ui';
import Taro, { useRouter, useDidShow } from '@tarojs/taro';

import banka from '@/assets/image/member/编组 <EMAIL>';
import kaidan from '@/assets/image/member/编组@2x.png';
import img1 from '@/assets/image/member/位图@2x.png';
import nv from '@/assets/image/member/矩形@2x.png';
import nan from '@/assets/image/member/nan.png';
import birthday from '@/assets/image/member/矩形@2x(1).png';
import EmptyImg from '@/assets/image/common/empty.png';
import ScrollList from '@/view/component/scroll-list/scroll-list.component';
import MemberCard from './components/memberCard';
import memberStore from './store/member';
import renewMemberStore from '../renew-member/store/renew-member';
import memberCardDetailStore from '../member-card-detail/store';
import memberBillingDetailStore from '../member-billing-detail/store';
import ConsumeList from './components/consumeList';
import styles from './member-detail-page.module.scss';
import { getMemberRecordDetailListNew } from '@/service/business/huiyuanxiangqingxiaofeimingxi';
import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';
import { colorEnmu } from '../select-card/color';

const PAGE_SIZE = 10;

// 获取昵称首字
const getFirstChar = (nickName: string | undefined) => {
  if (!nickName) return '';
  return nickName?.charAt(0);
};

const MemberDetailPage = () => {
  const router = useRouter();
  const { id } = router.params;
  // const id = 33;

  const [memberInfo, setMemberInfo]: any = useState({});

  const [activeTab, setActiveTab] = useState('card');

  const [cardData, setCardData] = useState({
    dataSource: [],
    total: 0,
  });
  const [detailData, setDetailData] = useState({
    dataSource: [],
    total: 0,
  });

  useDidShow(() => {
    // 这里可以添加获取会员详情的接口调用
    // console.log('获取会员ID:', id);
    service.member.tMemberController
      .getMemberDataDetail({ memberId: id } as any)
      .then((res) => {
        const { nickName, phonenumber, birthday, sex, avatar }: any =
          memberStore.memberDetail;
        setMemberInfo({
          ...(res || {}),
          nickName,
          phonenumber,
          birthday,
          sex,
          avatar,
        });
      });

    getList(1, 'card');
    getList(1, 'detail');
  });

  const getList = useCallback((pageNum: number, tab: string) => {
    if (tab === 'card') {
      service.businessRelation.tMemberCardRelationController
        .postRelationCardRTMemberList({
          pageNum,
          pageSize: PAGE_SIZE,
          memberId: id,
        } as any)
        .then((res: any) => {
          if (res?.data) {
            const data = (res.data || []).map((item, index) => {
              return {
                ...item,
                ...colorEnmu[index % 4],
              };
            });

            setCardData((v) => {
              return {
                dataSource:
                  pageNum === 1
                    ? data
                    : v.dataSource?.concat(data) ?? v.dataSource,
                total: res?.total ?? 0,
              };
            });
          }
        });
    } else {
      getMemberRecordDetailListNew({
        pageNum,
        pageSize: PAGE_SIZE,
        memberId: id,
      } as any).then((res: any) => {
        if (res?.data) {
          setDetailData((v) => {
            return {
              dataSource:
                pageNum === 1
                  ? res?.data
                  : v.dataSource?.concat(res?.data) ?? v.dataSource,
              total: res?.total ?? 0,
            };
          });
        }
      });
    }
  }, []);

  const renderItem = (item: any) => {
    if (activeTab === 'card') {
      return (
        <MemberCard
          card={item}
          handleRenewal={handleRenewal}
          handleReturnCard={handleReturnCard}
          handleCardDetail={handleCardDetail}
        />
      );
    }

    return <ConsumeList item={item} />;
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    getList(1, tab);
  };

  const handleBillingSettlement = () => {
    if (cardData.total === 0) {
      return Taro.showToast({ title: '请先办理会员卡', icon: 'none' });
    }

    memberBillingDetailStore.setMemberInfo({
      ...memberInfo,
      memberId: id,
    });
    if (cardData.dataSource.length === 1) {
      memberBillingDetailStore.setCard(cardData.dataSource[0]);
      Taro.navigateTo({
        url: `/member/billing-settlement/billing-settlement-page`,
      });
    } else {
      Taro.navigateTo({
        url: `/member/select-card/select-card-page?memberId=${id}&action=billingSettlement`,
      });
    }
  };

  const handleOpenCard = () => {
    Taro.navigateTo({
      url: `/member/newcard-add/newcard-add-page?memberId=${id}`,
    });
  };

  // 续卡
  const handleRenewal = (card) => {
    renewMemberStore.setCard(card);
    Taro.navigateTo({
      url: `/member/renew-member/renew-member-page?memberId=${id}`,
    });
  };

  // 会员卡详情
  const handleCardDetail = (card) => {
    memberCardDetailStore.setCard(card);
    Taro.navigateTo({
      url: `/member/member-card-detail/member-card-detail-page`,
    });
  };

  const handleReturnCard = (cardId) => {
    console.log('退卡:', cardId);
    // 这里添加退卡的逻辑
  };

  return (
    <PageWithNav className={styles.container} showNavBar title="会员详情">
      {/* 会员基本信息 */}
      <View className={styles.memberInfoCard}>
        <View className={styles.memberAvatar}>
          {memberInfo.avatar ? (
            <Image
              className={styles.avatarImage}
              src={memberInfo.avatar}
            />
          ) : (
            <View className={styles.avatarText}>
              {getFirstChar(memberInfo.nickName)}
            </View>
          )}
        </View>

        <View className={styles.memberInfo}>
          <View className={styles.nameRow}>
            <Text className={styles.memberName}>
              {memberInfo.nickName || ''}
            </Text>
            <Image
              style={{ width: 16, height: 16, marginRight: 5 }}
              src={memberInfo.sex === 1 ? nan : nv}
            />
            {memberInfo.phonenumber && (
              <AtIcon
                value="phone"
                size="10"
                color="#999"
                onClick={() => {
                  Taro.makePhoneCall({
                    phoneNumber: memberInfo.phonenumber,
                    success: () => {
                      console.log('拨打电话成功');
                    },
                    fail: (err) => {
                      console.error('拨打电话失败', err);
                      Taro.showToast({
                        title: '拨打电话失败',
                        icon: 'none',
                        duration: 2000
                      });
                    }
                  });
                }}
              />
            )}
          </View>

          <View className={styles.birthdayRow}>
            <Image
              style={{
                width: 16,
                height: 16,
              }}
              src={birthday}
            />
            <Text style={{ marginLeft: 5 }}>{memberInfo.birthday}</Text>
          </View>

          <View className={styles.staffRow}>
            <Text>
              顾问：{memberInfo.advisor}
            </Text>
          </View>

          <View className={styles.tagRow}>
            {!!memberInfo.highFrequency && (
              <View className={styles.tag}>高频</View>
            )}
          </View>
        </View>

        {/* <View className={styles.arrowRight}>
          <AtIcon
            value="chevron-right"
            size="20"
            color="#ccc"
            onClick={handleCardDetail}
          />
        </View> */}
      </View>

      {/* 统计信息 */}
      <View className={styles.statsContainer}>
        <View className={styles.statItem}>
          <Text className={styles.statLabel}>累计项目消费</Text>
          <Text className={styles.statValue}>
            {memberInfo.totalConsumCnt || 0}次
          </Text>
        </View>

        <View className={styles.statItem}>
          <Text className={styles.statLabel}>累计充值</Text>
          <Text className={styles.statValue}>
            {memberInfo?.totalRecharge?.toFixed(2) || 0}元
          </Text>
        </View>

        <View className={styles.statItem}>
          <Text className={styles.statLabel}>累计消费</Text>
          <Text className={styles.statValue}>
            {memberInfo?.totalConsum?.toFixed(2) || 0}元
          </Text>
        </View>
      </View>

      {/* 会员卡信息 */}
      <View className={styles.cardContainer}>
        <View className={styles.cardTabs}>
          <View
            className={`${styles.cardTab}`}
            onClick={() => handleTabChange('card')}
          >
            <Text
              className={`${styles.cardDes} ${
                activeTab === 'card' ? styles.activeTab : ''
              }`}
            >
              会员卡<Text className={styles.cardCount}>{cardData.total}</Text>
            </Text>
          </View>

          <View
            className={`${styles.cardTab}`}
            onClick={() => handleTabChange('detail')}
          >
            <Text
              className={`${styles.cardDes} ${
                activeTab === 'detail' ? styles.activeTab : ''
              }`}
            >
              消费明细
              <Text className={styles.cardCount}>{detailData.total}</Text>
            </Text>
          </View>
        </View>
      </View>

      <ScrollList
        dataSource={
          activeTab === 'card' ? cardData.dataSource : detailData.dataSource
        }
        total={activeTab === 'card' ? cardData.total : detailData.total}
        pageSize={PAGE_SIZE}
        renderItem={renderItem}
        onLoadMore={(page) => getList(page, activeTab)}
        className={styles.scrollList}
        keyExtractor={(item: any) => item?.id?.toString()}
        emptyComponent={
          <View className={styles.emptyList}>
            <Image className={styles.empty} src={EmptyImg} />
            <Text className={styles.emptyText}>
              {activeTab === 'card' ? '暂无会员卡' : '暂无消费记录'}
            </Text>
          </View>
        }
      />

      {/* 底部按钮 */}
      <View className={styles.bottomButton}>
        <View
          className={styles.openCardButton}
          onClick={handleBillingSettlement}
        >
          <View>
            <Image className={styles.icon} src={kaidan} />
          </View>
          <Text className={styles.bottomText}>开单</Text>
        </View>
        <View className={styles.openCardButton} onClick={handleOpenCard}>
          <View>
            <Image className={styles.icon} src={banka} />
          </View>
          <Text className={styles.bottomText}>办卡</Text>
        </View>
      </View>
    </PageWithNav>
  );
};
export default MemberDetailPage;
