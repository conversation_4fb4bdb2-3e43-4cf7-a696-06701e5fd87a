import { View, Input, Button, Label, RichText } from "@tarojs/components";
import Taro from "@tarojs/taro";
import useWindowArea from "@/hook/windowArea";
import React, { useMemo, useRef, useState } from "react";
import { validatePhone } from "@/util/validate-util";
import { AtIcon } from "taro-ui";

const COUNTDOWN = 60;

const PasswordRecoveryPage: React.FC = () => {
  const { bottomArea, topArea, navArea } = useWindowArea()

  const [recoveryForm, setRecoveryForm] = useState({
    username: "",
    verifyCode: ""
  });

  const [showSend, setShowSend] = useState(true);
  const [countdown, setCountdown] = useState(COUNTDOWN);

  const countdownRef = useRef(COUNTDOWN)
  const timer = useRef<NodeJS.Timeout>();

  // 更新找回表单state信息
  const updateForm = (params: Record<string, string>) => {
    setRecoveryForm(state => ({
      ...state,
      ...params
    }))
  }

  // const phoneNumber = useMemo(() => {
  // 	console.log(recoveryForm.username.replace(/\s/g, '').replace(/(\d{3})(\d{0,4})(\d{0,4})/, '$1 $2 $3'))
  // 	return recoveryForm.username.replace(/^(.{3})(.*)(.{4})$/, '$1 $2 $3')
  // }, [recoveryForm.username])

  // const onPhoneNumberChange = (val: string) => {
  // 	updateForm({ username: val.replace(/[^\d]/g, "") })
  // }

  const sendVerifyCode = async () => {
    if (!recoveryForm.username.trim()) {
      Taro.showToast({
        title: '请输入用户名/手机号',
        icon: 'none'
      })
      return
    }

    if (!validatePhone(recoveryForm.username)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    try {
      service.auth.tokenController.postAuthSendSmsCode({
        phonenumber: recoveryForm.username
      }).then(res => {
        setShowSend(false);
        Taro.showToast({
          title: '获取成功，请查收短信验证码',
          icon: 'none'
        })
        timer.current = setInterval(() => {
          setCountdown(--countdownRef.current);
          if (countdownRef.current === 0) {
            setShowSend(true)
            clearInterval(timer.current);
            countdownRef.current = COUNTDOWN;
          }
        }, 1000)
      })
    } catch (error) {
      Taro.showToast({
        title: '获取验证码失败',
        icon: 'none'
      })
    }
  }

  /**
   * 账号密码登录
   * @returns 
   */
  const nextStep = async () => {
    if (!recoveryForm.username.trim()) {
      Taro.showToast({
        title: '请输入用户名/手机号',
        icon: 'none'
      })
      return
    }

    if (!validatePhone(recoveryForm.username)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    if (!recoveryForm.verifyCode.trim()) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }

    try {
      // todo 调用验证接口

    } catch (error) {

    }
  }

  /**
   * 验证码登录
   */
  const loginWithVerityCode = async () => {
    if (!recoveryForm.username.trim()) {
      Taro.showToast({
        title: '请输入用户名/手机号',
        icon: 'none'
      })
      return
    }

    if (!validatePhone(recoveryForm.username)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    if (!recoveryForm.verifyCode.trim()) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }

    try {
      const res = await service.auth.tokenController.postLoginSmsBusiness({
        phonenumber: recoveryForm.username,
        smscode: recoveryForm.verifyCode
      })
      setLoginInfo(res?.access_token);

      Taro.navigateTo({ url: "/view/page/tab/home/<USER>" });

    } catch (error) {
      Taro.showToast({
        title: '登录失败',
        icon: 'error'
      })
    }
  }

  const setLoginInfo = (token) => {
    let loginInfo: any = {};
    loginInfo.token = token;
    Taro.setStorageSync("loginInfo", loginInfo);
    service.business.businessController.getIdsUserId().then((res) => {
      if (res && res.length > 0) {
        // TODO 先默认取第一个
        loginInfo.businessId = res[0];
        Taro.setStorageSync("loginInfo", loginInfo);
      }
    })
  }

  return (
    <View className="w-full h-full bg-gradient-to-b from-[rgba(253,210,68,0.26)] to-[rgba(255,248,225,0)] overflow-hidden box-border flex flex-col gap-3"
      style={{ paddingBottom: bottomArea }}>
      {/* 导航栏 */}
      <View className="w-full relative flex items-center justify-center"
        style={{ height: navArea + topArea, paddingTop: topArea }}>
        <View className="absolute left-3 text-base" onClick={() => Taro.navigateBack()}>
          <AtIcon value="chevron-left" size="13" color="#000000" />
        </View>
      </View>

      {/* 顶部标题区域 */}
      <View className="h-[120px] relative">
        <View className="absolute bottom-5 left-6 text-[28px] font-bold">
          <RichText nodes="验证码登录" />
        </View>
      </View>

      {/* 表单区域 */}
      <View className="px-5 mt-2">
        <View className="bg-white/90 rounded-2xl p-5 shadow-lg backdrop-blur-sm">
          <View className="mb-4">
            <View className="text-[#1f1f1f] font-bold mb-3 text-base">
              用户名/手机号
            </View>
            <View className="bg-[#f8f9fa] rounded-xl p-3 border border-[#e9ecef] flex items-center">
              <Input
                className="bg-transparent text-base text-[#333] flex-1"
                type='number'
                value={recoveryForm.username}
                placeholder='请输入用户名/手机号'
                placeholderClass="text-[#999]"
                cursorSpacing={20}
                onInput={e => updateForm({ username: e.detail.value })}
              />
              {recoveryForm.username && (
                <View
                  className="w-5 h-5 bg-[#ccc] rounded-full flex items-center justify-center ml-2"
                  onClick={() => updateForm({ username: '' })}
                >
                  <View className="text-white text-xs">×</View>
                </View>
              )}
            </View>
          </View>
          <View className="mb-2">
            <View className="text-[#1f1f1f] font-bold mb-3 text-base">
              验证码
            </View>
            <View className="bg-[#f8f9fa] rounded-xl p-3 border border-[#e9ecef] flex items-center">
              <Input
                className="bg-transparent text-base text-[#333] flex-1"
                type='number'
                value={recoveryForm.verifyCode}
                placeholder='请输入验证码'
                placeholderClass="text-[#999]"
                cursorSpacing={20}
                onInput={e => updateForm({ verifyCode: e.detail.value })}
              />
              {recoveryForm.verifyCode && (
                <View
                  className="w-5 h-5 bg-[#ccc] rounded-full flex items-center justify-center ml-2 mr-2"
                  onClick={() => updateForm({ verifyCode: '' })}
                >
                  <View className="text-white text-xs">×</View>
                </View>
              )}
              {showSend ? (
                <Button
                  className="px-3 py-1 rounded-full bg-transparent text-sm leading-none border-0 text-[#FDD244] after:border-none"
                  hoverClass="bg-[rgba(253,210,68,0.297)]"
                  onClick={sendVerifyCode}
                >
                  获取验证码
                </Button>
              ) : (
                <Label className="px-3 py-1 text-sm text-[#FDD244] leading-none">
                  {countdown}秒后重新发送
                </Label>
              )}
            </View>
          </View>
        </View>
      </View>

      {/* 弹性空间 */}
      <View className="flex-1"></View>

      {/* 登录按钮 */}
      <View className="flex flex-col gap-3 items-center text-[#999999] mt-25 px-5 mb-8">
        <Button
          className={`w-full h-12 rounded-full border-0 text-lg font-bold after:border-none ${recoveryForm.verifyCode
              ? 'bg-[#FDD244] text-white shadow-lg'
              : 'bg-[#E0E0E0] text-white'
            }`}
          hoverClass={recoveryForm.verifyCode ? "bg-[#fdd244e0] shadow-xl" : ""}
          onClick={loginWithVerityCode}
        >
          登录
        </Button>
      </View>
    </View>
  );
};

export default PasswordRecoveryPage;
