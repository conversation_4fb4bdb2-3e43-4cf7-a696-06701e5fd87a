import { View, Text, Image } from "@tarojs/components";
import { observer } from "mobx-react";

interface Props {
  title?: string;
  description?: string;
  image?: string;
  imageSize?: 'small' | 'medium' | 'large';
  action?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const EmptyState: React.FC<Props> = observer((props) => {
  const {
    title = "暂无数据",
    description,
    image,
    imageSize = 'medium',
    action,
    className = "",
    style,
  } = props;

  const sizeClasses = {
    small: "w-16 h-16",
    medium: "w-24 h-24", 
    large: "w-32 h-32",
  };

  const defaultImage = require("@/assets/image/empty/default.png");

  return (
    <View 
      className={`flex flex-col items-center justify-center py-12 px-6 ${className}`}
      style={style}
    >
      {/* 图片 */}
      <View className="mb-4">
        <Image
          className={`${sizeClasses[imageSize]} opacity-60`}
          src={image || defaultImage}
          mode="aspectFit"
        />
      </View>

      {/* 标题 */}
      <Text className="text-lg font-medium text-gray-800 mb-2 text-center">
        {title}
      </Text>

      {/* 描述 */}
      {description && (
        <Text className="text-sm text-gray-500 text-center mb-6 leading-relaxed">
          {description}
        </Text>
      )}

      {/* 操作按钮 */}
      {action && (
        <View className="mt-2">
          {action}
        </View>
      )}
    </View>
  );
});

export default EmptyState;
