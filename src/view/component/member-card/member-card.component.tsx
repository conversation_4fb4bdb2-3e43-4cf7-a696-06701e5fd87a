import { View, Text } from "@tarojs/components";
import { observer } from "mobx-react";
import { pinyin } from "pinyin-pro";

interface MemberInfo {
  name?: string;
  nickName?: string;
  no?: string;
  memberId?: string;
  level?: string;
  cardLevel?: string;
  phonenumber?: string;
  balance?: number;
  birthday?: string;
  registerTime?: string;
  weixinOpenid?: string;
}

interface Props {
  onClick?: () => void;
  style?: React.CSSProperties;
  className?: string;
  
  // 会员信息
  memberInfo?: MemberInfo;
  
  // 显示模式
  mode?: 'simple' | 'detailed';
  
  // 颜色配置
  colors?: {
    name?: string;
    pinyin?: string;
    no?: string;
    level?: string;
    background?: string;
  };
  
  // 兼容旧的 props
  no?: string;
  name?: string;
  level?: string;
  name_color?: string;
  pinyin_color?: string;
  no_color?: string;
  level_color?: string;
  bg_color?: string;
  memberDetail?: any;
}

const DEFAULT_COLORS = {
  name: "#000000",
  pinyin: "#000000", 
  no: "#000000",
  level: "#000000",
  background: "#FDD244",
};

const MemberCard: React.FC<Props> = observer((props) => {
  const {
    onClick,
    style,
    className = "",
    mode = 'simple',
    colors = {},
    memberInfo,
    memberDetail,
    // 兼容旧的 props
    name,
    no,
    level,
    name_color,
    pinyin_color,
    no_color,
    level_color,
    bg_color,
  } = props;

  // 合并颜色配置
  const finalColors = {
    ...DEFAULT_COLORS,
    ...colors,
    // 兼容旧的颜色配置
    ...(name_color && { name: name_color }),
    ...(pinyin_color && { pinyin: pinyin_color }),
    ...(no_color && { no: no_color }),
    ...(level_color && { level: level_color }),
    ...(bg_color && { background: bg_color }),
  };

  // 合并会员信息
  const finalMemberInfo = {
    ...memberInfo,
    ...memberDetail,
    // 兼容旧的 props
    ...(name && { name }),
    ...(no && { no }),
    ...(level && { level }),
  };

  const displayName = finalMemberInfo.name || finalMemberInfo.nickName || '';
  const displayNo = finalMemberInfo.no || finalMemberInfo.memberId || '';
  const displayLevel = finalMemberInfo.level || finalMemberInfo.cardLevel || '';

  if (mode === 'detailed') {
    return (
      <View
        className={`relative w-[656px] h-[390px] rounded-3xl text-white transition-all duration-200 overflow-hidden cursor-pointer flex flex-col p-5 ${className}`}
        onClick={onClick}
        style={{
          backgroundColor: finalColors.background,
          ...style,
        }}
      >
        <Text className="text-2xl font-bold mb-2" style={{ color: finalColors.name }}>
          姓名: {displayName}
        </Text>
        <Text className="text-lg font-semibold mb-2" style={{ color: finalColors.no }}>
          会员卡号: No. {displayNo}
        </Text>
        <Text className="text-lg font-semibold mb-2" style={{ color: finalColors.level }}>
          会员等级: {displayLevel}
        </Text>
        
        {finalMemberInfo.phonenumber && (
          <Text className="text-base mb-1" style={{ color: finalColors.level }}>
            手机号: {finalMemberInfo.phonenumber}
          </Text>
        )}
        
        {finalMemberInfo.balance !== undefined && (
          <Text className="text-base mb-1" style={{ color: finalColors.level }}>
            余额: ¥{finalMemberInfo.balance}
          </Text>
        )}
        
        {finalMemberInfo.birthday && (
          <Text className="text-base mb-1" style={{ color: finalColors.level }}>
            生日: {finalMemberInfo.birthday}
          </Text>
        )}
        
        {finalMemberInfo.registerTime && (
          <Text className="text-sm" style={{ color: finalColors.level }}>
            注册时间: {finalMemberInfo.registerTime}
          </Text>
        )}
      </View>
    );
  }

  // 简单模式
  return (
    <View
      className={`relative w-full h-auto rounded-3xl text-white transition-all duration-200 overflow-hidden cursor-pointer flex flex-col p-5 ${className}`}
      onClick={onClick}
      style={{
        backgroundColor: finalColors.background,
        ...style,
      }}
    >
      <Text className="text-2xl font-bold mb-1" style={{ color: finalColors.name }}>
        {displayName}
      </Text>
      
      {displayName && (
        <Text className="text-lg font-semibold mb-2 opacity-80" style={{ color: finalColors.pinyin }}>
          {pinyin(displayName, { toneType: "none" })}
        </Text>
      )}
      
      <Text className="text-lg font-semibold mb-2" style={{ color: finalColors.no }}>
        No. {displayNo}
      </Text>
      
      <Text className="text-lg font-semibold" style={{ color: finalColors.level }}>
        {displayLevel}
      </Text>
    </View>
  );
});

export default MemberCard;
