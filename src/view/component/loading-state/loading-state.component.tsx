import { View, Text } from "@tarojs/components";
import { observer } from "mobx-react";

interface Props {
  text?: string;
  size?: 'small' | 'medium' | 'large';
  type?: 'spinner' | 'dots' | 'pulse';
  className?: string;
  style?: React.CSSProperties;
}

const LoadingState: React.FC<Props> = observer((props) => {
  const {
    text = "加载中...",
    size = 'medium',
    type = 'spinner',
    className = "",
    style,
  } = props;

  const sizeClasses = {
    small: "w-4 h-4",
    medium: "w-6 h-6",
    large: "w-8 h-8",
  };

  const textSizeClasses = {
    small: "text-sm",
    medium: "text-base", 
    large: "text-lg",
  };

  const renderSpinner = () => (
    <View 
      className={`${sizeClasses[size]} border-2 border-gray-200 border-t-primary-500 rounded-full animate-spin`}
    />
  );

  const renderDots = () => (
    <View className="flex space-x-1">
      <View className={`${sizeClasses[size]} bg-primary-500 rounded-full animate-bounce`} style={{ animationDelay: '0ms' }} />
      <View className={`${sizeClasses[size]} bg-primary-500 rounded-full animate-bounce`} style={{ animationDelay: '150ms' }} />
      <View className={`${sizeClasses[size]} bg-primary-500 rounded-full animate-bounce`} style={{ animationDelay: '300ms' }} />
    </View>
  );

  const renderPulse = () => (
    <View className={`${sizeClasses[size]} bg-primary-500 rounded-full animate-pulse`} />
  );

  const renderLoader = () => {
    switch (type) {
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      default:
        return renderSpinner();
    }
  };

  return (
    <View 
      className={`flex flex-col items-center justify-center py-8 ${className}`}
      style={style}
    >
      {/* 加载动画 */}
      <View className="mb-3">
        {renderLoader()}
      </View>

      {/* 加载文字 */}
      {text && (
        <Text className={`${textSizeClasses[size]} text-gray-600 text-center`}>
          {text}
        </Text>
      )}
    </View>
  );
});

export default LoadingState;
